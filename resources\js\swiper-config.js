/**
 * Modern Swiper.js Configuration
 * Enhanced for Performance, Accessibility, and User Experience
 */

class ModernSwiperManager {
    constructor() {
        this.swipers = new Map();
        this.defaultConfig = this.getDefaultConfig();
        this.init();
    }

    getDefaultConfig() {
        return {
            // Performance optimizations
            watchSlidesProgress: true,
            watchSlidesVisibility: true,
            preloadImages: false,
            lazy: {
                loadPrevNext: true,
                loadPrevNextAmount: 2,
                loadOnTransitionStart: true,
            },
            // Smooth transitions
            speed: 600,
            // Touch/mouse interactions
            grabCursor: true,
            touchRatio: 1,
            touchAngle: 45,
            simulateTouch: true,
            // Accessibility
            a11y: {
                enabled: true,
                prevSlideMessage: "Previous slide",
                nextSlideMessage: "Next slide",
                firstSlideMessage: "This is the first slide",
                lastSlideMessage: "This is the last slide",
            },
            // Keyboard navigation
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            },
            // Mouse wheel support
            mousewheel: {
                forceToAxis: true,
                sensitivity: 1,
                releaseOnEdges: true,
            },
        };
    }

    init() {
        document.addEventListener("DOMContentLoaded", () => {
            this.initializeSliders();
            this.setupGlobalOptimizations();
            this.handleReducedMotion();
        });
    }

    initializeSliders() {
        // Initialize Hero Swiper
        this.initHeroSwiper();

        // Initialize Banner Swiper
        this.initBannerSwiper();

        // Initialize Partner Swiper
        this.initPartnerSwiper();

        // Initialize Category Swiper
        this.initCategorySwiper();
    }

    initHeroSwiper() {
        const element = document.getElementById("heroSwiper");
        if (!element) return;

        const config = {
            ...this.defaultConfig,
            loop: true,
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            effect: "slide",
            speed: 600,
            on: {
                init: function () {
                    this.autoplay.start();
                    this.el.classList.add("swiper-initialized");
                },
            },
        };

        const swiper = new Swiper(element, config);
        this.swipers.set("hero", swiper);
        this.setupHoverPause(element, swiper);
    }

    initBannerSwiper() {
        const element = document.getElementById("bannerSwiper");
        if (!element) return;

        const config = {
            ...this.defaultConfig,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            effect: "slide",
            speed: 600,
            on: {
                init: function () {
                    this.autoplay.start();
                    this.el.classList.add("swiper-initialized");
                },
            },
        };

        const swiper = new Swiper(element, config);
        this.swipers.set("banner", swiper);
        this.setupHoverPause(element, swiper);
    }

    initPartnerSwiper() {
        const element = document.getElementById("partnerSwiper");
        if (!element) return;

        const config = {
            ...this.defaultConfig,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            slidesPerView: "auto",
            spaceBetween: 20,
            centeredSlides: false,
            freeMode: {
                enabled: true,
                sticky: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 2,
                    spaceBetween: 15,
                },
                640: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 4,
                    spaceBetween: 25,
                },
                1024: {
                    slidesPerView: 5,
                    spaceBetween: 30,
                },
                1280: {
                    slidesPerView: 6,
                    spaceBetween: 30,
                },
                1536: {
                    slidesPerView: 7,
                    spaceBetween: 35,
                },
            },
            on: {
                init: function () {
                    this.autoplay.start();
                    this.el.classList.add("swiper-initialized");
                },
            },
        };

        const swiper = new Swiper(element, config);
        this.swipers.set("partner", swiper);
        this.setupHoverPause(element, swiper);
    }

    initCategorySwiper() {
        const element = document.getElementById("categorySwiper");
        if (!element) return;

        const config = {
            ...this.defaultConfig,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            effect: "coverflow",
            coverflowEffect: {
                rotate: 15,
                stretch: 0,
                depth: 100,
                modifier: 2,
                slideShadows: true,
            },
            slidesPerView: "auto",
            spaceBetween: 20,
            centeredSlides: true,
            pagination: {
                el: "#categorySwiper .swiper-pagination",
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1,
                    spaceBetween: 15,
                    centeredSlides: true,
                },
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                    centeredSlides: false,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                    centeredSlides: false,
                },
                1280: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                    centeredSlides: false,
                },
            },
            on: {
                beforeInit: function () {
                    if (!this.support.transforms3d) {
                        this.params.effect = "slide";
                        this.params.centeredSlides = false;
                    }
                },
                init: function () {
                    this.autoplay.start();
                    this.el.classList.add("swiper-initialized");
                },
            },
        };

        const swiper = new Swiper(element, config);
        this.swipers.set("category", swiper);
        this.setupHoverPause(element, swiper);
    }

    setupHoverPause(element, swiper) {
        if (!swiper.autoplay) return;

        element.addEventListener("mouseenter", () => {
            swiper.autoplay.stop();
        });

        element.addEventListener("mouseleave", () => {
            swiper.autoplay.start();
        });
    }

    setupGlobalOptimizations() {
        // Intersection Observer for lazy loading sliders
        if ("IntersectionObserver" in window) {
            const sliderObserver = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            const slider = entry.target;
                            slider.classList.add("slider-visible");

                            // Trigger swiper update if needed
                            const swiperId = slider.id;
                            const swiperInstance = this.getSwiperById(swiperId);
                            if (swiperInstance) {
                                swiperInstance.update();
                            }
                        }
                    });
                },
                {
                    rootMargin: "50px",
                }
            );

            // Observe all sliders
            document.querySelectorAll(".swiper").forEach((slider) => {
                sliderObserver.observe(slider);
            });
        }

        // Handle window resize
        window.addEventListener(
            "resize",
            this.debounce(() => {
                this.swipers.forEach((swiper) => {
                    swiper.update();
                });
            }, 250)
        );
    }

    handleReducedMotion() {
        if (window.matchMedia("(prefers-reduced-motion: reduce)").matches) {
            // Disable autoplay for users who prefer reduced motion
            this.swipers.forEach((swiper) => {
                if (swiper.autoplay) {
                    swiper.autoplay.stop();
                }
            });
        }
    }

    getSwiperById(id) {
        const swiperName = id.replace("Swiper", "").toLowerCase();
        return this.swipers.get(swiperName);
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the Swiper Manager
const swiperManager = new ModernSwiperManager();

// Export for global access if needed
window.swiperManager = swiperManager;
