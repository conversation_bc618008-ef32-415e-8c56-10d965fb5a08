<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class BannerController extends Controller
{
    /**
     * Display a listing of banners.
     */
    public function index(): View
    {
        $banners = Banner::ordered()->paginate(10);
        
        return view('admin.banners.index', compact('banners'));
    }

    /**
     * Show the form for creating a new banner.
     */
    public function create(): View
    {
        return view('admin.banners.create');
    }

    /**
     * Store a newly created banner.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'image' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'link_url' => 'nullable|url',
            'link_text' => 'required|string|max:50',
            'link_target' => 'required|in:_self,_blank',
            'button_style' => 'required|in:primary,secondary,outline',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        $data = $request->except(['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        Banner::create($data);

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner created successfully!');
    }

    /**
     * Display the specified banner.
     */
    public function show(Banner $banner): View
    {
        return view('admin.banners.show', compact('banner'));
    }

    /**
     * Show the form for editing the specified banner.
     */
    public function edit(Banner $banner): View
    {
        return view('admin.banners.edit', compact('banner'));
    }

    /**
     * Update the specified banner.
     */
    public function update(Request $request, Banner $banner): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'link_url' => 'nullable|url',
            'link_text' => 'required|string|max:50',
            'link_target' => 'required|in:_self,_blank',
            'button_style' => 'required|in:primary,secondary,outline',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        $data = $request->except(['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($banner->image) {
                Storage::disk('public')->delete($banner->image);
            }
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        $banner->update($data);

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner updated successfully!');
    }

    /**
     * Remove the specified banner.
     */
    public function destroy(Banner $banner): RedirectResponse
    {
        // Delete image
        if ($banner->image) {
            Storage::disk('public')->delete($banner->image);
        }

        $banner->delete();

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner deleted successfully!');
    }

    /**
     * Toggle banner active status.
     */
    public function toggleActive(Banner $banner): RedirectResponse
    {
        $banner->update(['is_active' => !$banner->is_active]);
        
        // Clear cache
        $this->clearBannerCaches();

        $status = $banner->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Banner {$status} successfully!");
    }

    /**
     * Handle image upload.
     */
    private function handleImageUpload($file): string
    {
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs('banners', $filename, 'public');
    }

    /**
     * Clear banner-related caches.
     */
    private function clearBannerCaches(): void
    {
        Cache::forget('homepage_banners');
        Cache::flush(); // Clear all cache for now
    }
}
