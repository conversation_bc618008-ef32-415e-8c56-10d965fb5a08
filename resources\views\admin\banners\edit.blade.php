@extends('layouts.admin')

@section('title', 'Edit Banner')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="{{ route('admin.banners.index') }}" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Banner</h1>
            <p class="text-gray-600">Update banner information</p>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('admin.banners.update', $banner) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="form-input @error('title') border-red-500 @enderror" 
                                   value="{{ old('title', $banner->title) }}" 
                                   required>
                            @error('title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3" 
                                      class="form-input @error('description') border-red-500 @enderror"
                                      placeholder="Optional description for the banner">{{ old('description', $banner->description) }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="image" class="form-label">Banner Image</label>
                            @if($banner->image)
                                <div class="mb-3">
                                    <img src="{{ $banner->image_url }}" alt="{{ $banner->title }}" class="w-full h-32 object-cover rounded-lg">
                                </div>
                            @endif
                            <input type="file" 
                                   id="image" 
                                   name="image" 
                                   accept="image/*" 
                                   class="form-input @error('image') border-red-500 @enderror">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to keep current image. Recommended size: 1920x600px. Max size: 2MB</p>
                            @error('image')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Link Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Link Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="link_url" class="form-label">Link URL</label>
                            <input type="url" 
                                   id="link_url" 
                                   name="link_url" 
                                   class="form-input @error('link_url') border-red-500 @enderror" 
                                   value="{{ old('link_url', $banner->link_url) }}" 
                                   placeholder="https://example.com">
                            @error('link_url')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="link_text" class="form-label">Button Text *</label>
                            <input type="text" 
                                   id="link_text" 
                                   name="link_text" 
                                   class="form-input @error('link_text') border-red-500 @enderror" 
                                   value="{{ old('link_text', $banner->link_text) }}" 
                                   required>
                            @error('link_text')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="button_style" class="form-label">Button Style *</label>
                            <select id="button_style" 
                                    name="button_style" 
                                    class="form-input @error('button_style') border-red-500 @enderror" 
                                    required>
                                <option value="primary" {{ old('button_style', $banner->button_style) === 'primary' ? 'selected' : '' }}>Primary (Gradient)</option>
                                <option value="secondary" {{ old('button_style', $banner->button_style) === 'secondary' ? 'selected' : '' }}>Secondary (Solid)</option>
                                <option value="outline" {{ old('button_style', $banner->button_style) === 'outline' ? 'selected' : '' }}>Outline</option>
                            </select>
                            @error('button_style')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="text_position" class="form-label">Text Position *</label>
                            <select id="text_position" 
                                    name="text_position" 
                                    class="form-input @error('text_position') border-red-500 @enderror" 
                                    required>
                                <option value="left" {{ old('text_position', $banner->text_position) === 'left' ? 'selected' : '' }}>Left</option>
                                <option value="center" {{ old('text_position', $banner->text_position) === 'center' ? 'selected' : '' }}>Center</option>
                                <option value="right" {{ old('text_position', $banner->text_position) === 'right' ? 'selected' : '' }}>Right</option>
                            </select>
                            @error('text_position')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="text_color" class="form-label">Text Color *</label>
                            <select id="text_color" 
                                    name="text_color" 
                                    class="form-input @error('text_color') border-red-500 @enderror" 
                                    required>
                                <option value="white" {{ old('text_color', $banner->text_color) === 'white' ? 'selected' : '' }}>White</option>
                                <option value="dark" {{ old('text_color', $banner->text_color) === 'dark' ? 'selected' : '' }}>Dark</option>
                            </select>
                            @error('text_color')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input @error('sort_order') border-red-500 @enderror" 
                                   value="{{ old('sort_order', $banner->sort_order) }}" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Schedule Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Schedule Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="starts_at" class="form-label">Start Date</label>
                            <input type="datetime-local" 
                                   id="starts_at" 
                                   name="starts_at" 
                                   class="form-input @error('starts_at') border-red-500 @enderror" 
                                   value="{{ old('starts_at', $banner->starts_at ? $banner->starts_at->format('Y-m-d\TH:i') : '') }}">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to start immediately</p>
                            @error('starts_at')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="expires_at" class="form-label">End Date</label>
                            <input type="datetime-local" 
                                   id="expires_at" 
                                   name="expires_at" 
                                   class="form-input @error('expires_at') border-red-500 @enderror" 
                                   value="{{ old('expires_at', $banner->expires_at ? $banner->expires_at->format('Y-m-d\TH:i') : '') }}">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to never expire</p>
                            @error('expires_at')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ old('is_active', $banner->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.banners.index') }}" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Banner</button>
        </div>
    </form>
</div>
@endsection
