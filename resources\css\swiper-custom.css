/* ===================================
   MODERN SWIPER.JS CUSTOM STYLES
   Enhanced for Performance & UX
   =================================== */

/* ===== GLOBAL SWIPER OPTIMIZATIONS ===== */
.swiper {
    /* Hardware acceleration for smooth performance */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.swiper-slide {
    /* Optimize slide rendering */
    transform: translateZ(0);
    backface-visibility: hidden;
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== ENHANCED NAVIGATION BUTTONS ===== */
.swiper-button-next,
.swiper-button-prev {
    /* Modern button styling */
    width: 48px !important;
    height: 48px !important;
    margin-top: -24px !important;
    
    /* Enhanced visual effects */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Better positioning */
    z-index: 10;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.swiper-button-next:active,
.swiper-button-prev:active {
    transform: scale(0.95);
}

/* Custom arrow icons */
.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 18px !important;
    font-weight: 600;
}

/* ===== ENHANCED PAGINATION ===== */
.swiper-pagination {
    /* Better positioning */
    bottom: 20px !important;
    z-index: 10;
}

.swiper-pagination-bullet {
    /* Modern bullet styling */
    width: 12px !important;
    height: 12px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    border: 2px solid rgba(255, 255, 255, 0.8);
    opacity: 1 !important;
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.swiper-pagination-bullet-active {
    background: #ffffff !important;
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.swiper-pagination-bullet:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.8) !important;
}

/* Dynamic bullets styling */
.swiper-pagination-bullet-active-main {
    background: #3b82f6 !important;
}

/* Progress bar pagination */
.swiper-pagination-progressbar {
    background: rgba(255, 255, 255, 0.2) !important;
    height: 4px !important;
}

.swiper-pagination-progressbar-fill {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6) !important;
    border-radius: 2px;
}

/* ===== HERO SLIDER SPECIFIC STYLES ===== */
.hero-swiper .swiper-slide {
    /* Optimize hero slide performance */
    will-change: transform, opacity;
}

.hero-swiper .swiper-button-next,
.hero-swiper .swiper-button-prev {
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-swiper .swiper-button-next:hover,
.hero-swiper .swiper-button-prev:hover {
    background: rgba(255, 255, 255, 0.25) !important;
}

/* ===== BANNER SLIDER SPECIFIC STYLES ===== */
.banner-swiper .swiper-button-next,
.banner-swiper .swiper-button-prev {
    background: rgba(0, 0, 0, 0.5) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.banner-swiper .swiper-button-next:hover,
.banner-swiper .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.7) !important;
}

/* ===== PARTNER SLIDER SPECIFIC STYLES ===== */
.partner-swiper .swiper-slide {
    /* Auto width for flexible partner cards */
    width: auto !important;
    min-width: 200px;
}

.partner-swiper .swiper-button-next,
.partner-swiper .swiper-button-prev {
    background: white !important;
    color: #6b7280 !important;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.partner-swiper .swiper-button-next:hover,
.partner-swiper .swiper-button-prev:hover {
    background: #f9fafb !important;
    color: #374151 !important;
    border-color: #d1d5db;
}

/* ===== CATEGORY SLIDER SPECIFIC STYLES ===== */
.category-swiper .swiper-slide {
    /* Enhanced 3D effect preparation */
    transform-style: preserve-3d;
}

.category-swiper .swiper-button-next,
.category-swiper .swiper-button-prev {
    background: rgba(0, 0, 0, 0.3) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.category-swiper .swiper-button-next:hover,
.category-swiper .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.5) !important;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
        width: 40px !important;
        height: 40px !important;
        margin-top: -20px !important;
    }
    
    .swiper-button-next::after,
    .swiper-button-prev::after {
        font-size: 14px !important;
    }
    
    .swiper-pagination-bullet {
        width: 10px !important;
        height: 10px !important;
    }
}

@media (max-width: 480px) {
    .swiper-button-next,
    .swiper-button-prev {
        width: 36px !important;
        height: 36px !important;
        margin-top: -18px !important;
    }
    
    .swiper-pagination {
        bottom: 15px !important;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.swiper-button-next:focus,
.swiper-button-prev:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.swiper-pagination-bullet:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.swiper-lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.swiper-lazy-loaded {
    opacity: 1;
}

/* Preload optimization */
.swiper-slide img {
    will-change: transform;
}

/* ===== LOADING STATES ===== */
.swiper-slide-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
    .swiper-slide,
    .swiper-button-next,
    .swiper-button-prev,
    .swiper-pagination-bullet {
        transition: none !important;
        animation: none !important;
    }
    
    .swiper-button-next:hover,
    .swiper-button-prev:hover {
        transform: none !important;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    .partner-swiper .swiper-button-next,
    .partner-swiper .swiper-button-prev {
        background: rgba(31, 41, 55, 0.9) !important;
        color: #d1d5db !important;
        border-color: rgba(75, 85, 99, 0.5);
    }
    
    .partner-swiper .swiper-button-next:hover,
    .partner-swiper .swiper-button-prev:hover {
        background: rgba(17, 24, 39, 0.95) !important;
        color: #f3f4f6 !important;
    }
}

/* ===== CUSTOM EFFECTS ===== */
.swiper-slide-shadow-left,
.swiper-slide-shadow-right {
    pointer-events: none;
    position: absolute;
    z-index: 10;
    top: 0;
    width: 50px;
    height: 100%;
    transition: opacity 0.3s;
}

.swiper-slide-shadow-left {
    left: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.2), transparent);
}

.swiper-slide-shadow-right {
    right: 0;
    background: linear-gradient(to left, rgba(0, 0, 0, 0.2), transparent);
}
