@extends('layouts.admin')

@section('title', 'Banners')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Banners</h1>
            <p class="text-gray-600">Manage home page banners and promotional content</p>
        </div>
        <a href="{{ route('admin.banners.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Add Banner
        </a>
    </div>

    <!-- Banners Table -->
    <div class="card">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banner</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($banners as $banner)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-16 w-24">
                                        <img class="h-16 w-24 rounded-lg object-cover" 
                                             src="{{ $banner->image_url }}" 
                                             alt="{{ $banner->title }}">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $banner->title }}</div>
                                        @if($banner->description)
                                            <div class="text-sm text-gray-500">{{ Str::limit($banner->description, 50) }}</div>
                                        @endif
                                        @if($banner->link_url)
                                            <div class="text-xs text-blue-600">{{ $banner->link_url }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @switch($banner->status)
                                    @case('active')
                                        <span class="badge-success">Active</span>
                                        @break
                                    @case('inactive')
                                        <span class="badge-gray">Inactive</span>
                                        @break
                                    @case('scheduled')
                                        <span class="badge-warning">Scheduled</span>
                                        @break
                                    @case('expired')
                                        <span class="badge-danger">Expired</span>
                                        @break
                                @endswitch
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @if($banner->starts_at)
                                    <div>Start: {{ $banner->starts_at->format('M j, Y') }}</div>
                                @endif
                                @if($banner->expires_at)
                                    <div>End: {{ $banner->expires_at->format('M j, Y') }}</div>
                                @endif
                                @if(!$banner->starts_at && !$banner->expires_at)
                                    <span class="text-gray-400">Always active</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $banner->sort_order }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{{ route('admin.banners.edit', $banner) }}" 
                                   class="text-blue-600 hover:text-blue-900">Edit</a>
                                
                                <form method="POST" action="{{ route('admin.banners.toggle-active', $banner) }}" class="inline">
                                    @csrf
                                    <button type="submit" 
                                            class="{{ $banner->is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900' }}">
                                        {{ $banner->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>
                                
                                <form method="POST" action="{{ route('admin.banners.destroy', $banner) }}" 
                                      class="inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this banner?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <div class="space-y-2">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    <p>No banners found</p>
                                    <a href="{{ route('admin.banners.create') }}" class="text-blue-600 hover:text-blue-800">Create your first banner</a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if($banners->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $banners->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
