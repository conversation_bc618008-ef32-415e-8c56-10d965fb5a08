@extends('layouts.app')

@section('content')
<article class="min-h-screen bg-white">
    <!-- Hero Section -->
    <header class="relative">
        @if($blog->featured_image ?? false)
            <div class="aspect-w-16 aspect-h-9 lg:aspect-h-6">
                <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                     alt="{{ $blog->title }}" 
                     class="w-full h-96 lg:h-[500px] object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
            </div>
        @else
            <div class="h-96 lg:h-[500px] bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 flex items-center justify-center">
                <div class="text-center text-white">
                    <div class="text-6xl lg:text-8xl font-bold mb-4">{{ substr($blog->title ?? 'Blog', 0, 1) }}</div>
                </div>
            </div>
        @endif
        
        <!-- Article Header -->
        <div class="absolute bottom-0 left-0 right-0 p-8 lg:p-12 text-white">
            <div class="max-w-4xl mx-auto">
                <!-- Breadcrumbs -->
                <nav class="mb-4">
                    <ol class="flex items-center space-x-2 text-sm">
                        <li><a href="{{ route('home') }}" class="hover:text-yellow-300 transition-colors">Home</a></li>
                        <li><span class="text-gray-300">/</span></li>
                        <li><a href="{{ route('blog.index') }}" class="hover:text-yellow-300 transition-colors">Blog</a></li>
                        @if($blog->primaryCategory ?? false)
                            <li><span class="text-gray-300">/</span></li>
                            <li><a href="{{ route('blog.category', $blog->primaryCategory->slug) }}" class="hover:text-yellow-300 transition-colors">{{ $blog->primaryCategory->name }}</a></li>
                        @endif
                    </ol>
                </nav>
                
                <!-- Categories -->
                <div class="flex flex-wrap gap-2 mb-4">
                    @if($blog->primaryCategory ?? false)
                        <span class="badge-primary bg-blue-600 text-white">{{ $blog->primaryCategory->name }}</span>
                    @endif
                    @if($blog->is_featured ?? false)
                        <span class="badge-warning bg-yellow-500 text-black">Featured</span>
                    @endif
                </div>
                
                <!-- Title -->
                <h1 class="text-3xl lg:text-5xl font-bold mb-4 leading-tight">
                    {{ $blog->title ?? 'Sample Blog Post Title' }}
                </h1>
                
                <!-- Excerpt -->
                @if($blog->excerpt ?? false)
                    <p class="text-xl lg:text-2xl text-gray-200 mb-6 leading-relaxed">
                        {{ $blog->excerpt }}
                    </p>
                @endif
                
                <!-- Meta Info -->
                <div class="flex flex-wrap items-center gap-6 text-sm">
                    @if($blog->author ?? false)
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                                <span class="text-white font-medium">{{ substr($blog->author->name, 0, 1) }}</span>
                            </div>
                            <span>By {{ $blog->author->name }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>{{ $blog->published_at->format('M j, Y') ?? 'Dec 15, 2024' }}</span>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ $blog->reading_time ?? 5 }} min read</span>
                    </div>
                    
                    @if($blog->views_count ?? false)
                        <div class="flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <span>{{ number_format($blog->views_count) }} views</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </header>

    <!-- Article Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="prose prose-lg max-w-none">
            @if($blog->content ?? false)
                {!! nl2br(e($blog->content)) !!}
            @else
                <!-- Demo Content -->
                <p class="text-lg leading-relaxed mb-6">
                    Welcome to this comprehensive guide on smart shopping and money-saving strategies! In today's economy, 
                    every dollar counts, and knowing how to maximize your purchasing power can make a significant difference 
                    in your monthly budget.
                </p>

                <h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">Why Smart Shopping Matters</h2>
                <p class="mb-6">
                    Smart shopping isn't just about finding the lowest price – it's about getting the best value for your money. 
                    This means considering factors like quality, durability, warranty, and long-term costs when making purchasing decisions.
                </p>

                <h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">Top Money-Saving Strategies</h2>
                <ol class="list-decimal list-inside space-y-3 mb-6">
                    <li><strong>Use Price Comparison Tools:</strong> Always compare prices across multiple retailers before making a purchase.</li>
                    <li><strong>Sign Up for Newsletters:</strong> Many stores offer exclusive discounts to email subscribers.</li>
                    <li><strong>Follow Social Media:</strong> Brands often share flash sales and special promotions on their social channels.</li>
                    <li><strong>Use Cashback Apps:</strong> Apps like Rakuten and Honey can help you earn money back on purchases.</li>
                    <li><strong>Time Your Purchases:</strong> Buy seasonal items at the end of their season for maximum savings.</li>
                </ol>

                <div class="bg-blue-50 border-l-4 border-blue-400 p-6 my-8">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                <strong>Pro Tip:</strong> Always check for coupon codes before completing your purchase. 
                                A simple Google search for "[store name] coupon code" can often save you 10-20% or more!
                            </p>
                        </div>
                    </div>
                </div>

                <h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">Building Your Savings Habit</h2>
                <p class="mb-6">
                    The key to successful money-saving is making it a habit. Start small by checking for deals on items 
                    you regularly purchase, then gradually expand your smart shopping practices to larger purchases.
                </p>

                <p class="mb-6">
                    Remember, the goal isn't to be cheap – it's to be smart with your money so you can afford the things 
                    that truly matter to you. Every dollar you save on everyday purchases is a dollar you can put toward 
                    your goals and dreams.
                </p>
            @endif
        </div>

        <!-- Social Sharing -->
        <div class="border-t border-gray-200 pt-8 mt-12">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Share this article</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-blue-600 hover:text-blue-700">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-blue-800 hover:text-blue-900">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-blue-700 hover:text-blue-800">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div class="text-right">
                    <p class="text-sm text-gray-500">Was this helpful?</p>
                    <div class="flex space-x-2 mt-1">
                        <button class="text-green-600 hover:text-green-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                            </svg>
                        </button>
                        <button class="text-red-600 hover:text-red-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Articles -->
    <section class="bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @forelse($relatedBlogs ?? [] as $relatedBlog)
                    @include('components.blog-card', ['blog' => $relatedBlog])
                @empty
                    <!-- Demo Related Articles -->
                    <article class="card-hover">
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-t-xl flex items-center justify-center">
                            <span class="text-white text-4xl font-bold">💳</span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="badge-success">Money Saving</span>
                                <span class="text-sm text-gray-500">Dec 12, 2024</span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2">Best Credit Cards for Cashback</h3>
                            <p class="text-gray-600 mb-4">Discover the top credit cards that offer the highest cashback rewards for your spending habits.</p>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="card-hover">
                        <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 rounded-t-xl flex items-center justify-center">
                            <span class="text-white text-4xl font-bold">🛒</span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="badge-warning">Shopping Guide</span>
                                <span class="text-sm text-gray-500">Dec 10, 2024</span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2">Holiday Shopping on a Budget</h3>
                            <p class="text-gray-600 mb-4">Smart strategies to enjoy the holiday season without breaking the bank.</p>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="card-hover">
                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-red-500 rounded-t-xl flex items-center justify-center">
                            <span class="text-white text-4xl font-bold">📱</span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="badge-primary">Technology</span>
                                <span class="text-sm text-gray-500">Dec 8, 2024</span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2">Best Apps for Finding Deals</h3>
                            <p class="text-gray-600 mb-4">Essential mobile apps that will help you save money on every purchase.</p>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Read More →
                            </a>
                        </div>
                    </article>
                @endforelse
            </div>
        </div>
    </section>
</article>
@endsection
