<article class="card-hover group">
    <!-- Featured Image -->
    <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-xl overflow-hidden">
        <?php if($blog->featured_image): ?>
            <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>" 
                 alt="<?php echo e($blog->title); ?>" 
                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                 loading="lazy">
        <?php else: ?>
            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center group-hover:from-blue-500 group-hover:to-purple-600 transition-colors duration-300">
                <span class="text-white text-4xl font-bold"><?php echo e(substr($blog->title, 0, 1)); ?></span>
            </div>
        <?php endif; ?>
    </div>

    <!-- Content -->
    <div class="p-6">
        <!-- Meta Information -->
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <?php if($blog->primaryCategory): ?>
                    <a href="<?php echo e(route('blog.category', $blog->primaryCategory->slug)); ?>" 
                       class="badge-primary hover:bg-blue-200 transition-colors duration-200">
                        <?php echo e($blog->primaryCategory->name); ?>

                    </a>
                <?php endif; ?>
                <?php if($blog->is_featured): ?>
                    <span class="badge-warning">Featured</span>
                <?php endif; ?>
            </div>
            <time class="text-sm text-gray-500" datetime="<?php echo e($blog->published_at->toISOString()); ?>">
                <?php echo e($blog->published_at->format('M j, Y')); ?>

            </time>
        </div>

        <!-- Title -->
        <h3 class="font-bold text-xl text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
            <a href="<?php echo e(route('blog.show', $blog->slug)); ?>"><?php echo e($blog->title); ?></a>
        </h3>

        <!-- Excerpt -->
        <p class="text-gray-600 mb-4 line-clamp-3 leading-relaxed"><?php echo e($blog->excerpt); ?></p>

        <!-- Footer -->
        <div class="flex items-center justify-between">
            <!-- Author Info -->
            <div class="flex items-center space-x-2">
                <?php if($blog->author): ?>
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium"><?php echo e(substr($blog->author->name, 0, 1)); ?></span>
                    </div>
                    <div class="text-sm">
                        <p class="text-gray-900 font-medium"><?php echo e($blog->author->name); ?></p>
                        <p class="text-gray-500"><?php echo e($blog->reading_time); ?> min read</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Read More Link -->
            <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" 
               class="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center space-x-1 group-hover:translate-x-1 transition-transform duration-200">
                <span>Read More</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- Additional Meta -->
        <?php if($blog->views_count > 0): ?>
            <div class="mt-3 pt-3 border-t border-gray-100">
                <div class="flex items-center justify-between text-xs text-gray-500">
                    <span class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        <span><?php echo e(number_format($blog->views_count)); ?> views</span>
                    </span>
                    
                    <?php if($blog->categories->count() > 1): ?>
                        <span>+<?php echo e($blog->categories->count() - 1); ?> more <?php echo e(Str::plural('category', $blog->categories->count() - 1)); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</article>
<?php /**PATH D:\Talha\blog-app\resources\views/components/blog-card.blade.php ENDPATH**/ ?>