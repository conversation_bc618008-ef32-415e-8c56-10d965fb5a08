<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    @if(isset($seoData))
        <title>{{ $seoData['title'] ?? config('app.name', 'BlogHub') }}</title>
        <meta name="description" content="{{ $seoData['description'] ?? 'Discover amazing blog content and lifestyle tips.' }}">
        @if(isset($seoData['keywords']))
            <meta name="keywords" content="{{ is_array($seoData['keywords']) ? implode(', ', $seoData['keywords']) : $seoData['keywords'] }}">
        @endif
        @if(isset($seoData['canonical']))
            <link rel="canonical" href="{{ $seoData['canonical'] }}">
        @endif

        <!-- Open Graph -->
        <meta property="og:title" content="{{ $seoData['title'] ?? config('app.name') }}">
        <meta property="og:description" content="{{ $seoData['description'] ?? 'Discover amazing blog content and lifestyle tips.' }}">
        <meta property="og:type" content="{{ $seoData['type'] ?? 'website' }}">
        <meta property="og:url" content="{{ $seoData['canonical'] ?? url()->current() }}">
        @if(isset($seoData['image']))
            <meta property="og:image" content="{{ $seoData['image'] }}">
        @endif

        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="{{ $seoData['title'] ?? config('app.name') }}">
        <meta name="twitter:description" content="{{ $seoData['description'] ?? 'Discover amazing blog content and lifestyle tips.' }}">
        @if(isset($seoData['image']))
            <meta name="twitter:image" content="{{ $seoData['image'] }}">
        @endif
    @else
        <title>{{ config('app.name', 'BlogHub') }}</title>
        <meta name="description" content="Discover amazing blog content and lifestyle tips.">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Swiper.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/css/lazy-loading.css', 'resources/js/app.js', 'resources/js/lazy-loading.js', 'resources/js/asset-optimizer.js'])

    <!-- Swiper.js -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Swiper Configuration (after Swiper.js is loaded) -->
    @vite(['resources/js/swiper-config.js'])

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            position: relative;
            transition: all 0.2s ease;
        }

        .card-hover {
            transition: all 0.3s ease;
            background: white;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(59, 130, 246, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            color: #374151;
            font-weight: 600;
            padding: 10px 22px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-1px);
        }

        .badge-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-gray {
            background: #f9fafb;
            color: #6b7280;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid #e5e7eb;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Clean animations */
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Simplified floating animations */
        .animate-bounce-gentle {
            animation: bounceGentle 3s ease-in-out infinite;
        }

        @keyframes bounceGentle {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
    
    @if (app()->environment('production'))
        <!-- Service Worker Registration -->
        <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/sw.js')
                        .then((registration) => {
                            console.log('SW registered: ', registration);

                            // Check for updates
                            registration.addEventListener('updatefound', () => {
                                const newWorker = registration.installing;
                                newWorker.addEventListener('statechange', () => {
                                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                        // New content available, show update notification
                                        if (confirm('New content is available. Refresh to update?')) {
                                            newWorker.postMessage({ type: 'SKIP_WAITING' });
                                            window.location.reload();
                                        }
                                    }
                                });
                            });
                        })
                        .catch((registrationError) => {
                            console.log('SW registration failed: ', registrationError);
                        });
                });
            }
        </script>
    @endif
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        @include('layouts.navigation')

        <!-- Page Content -->
        <main>
            @if(isset($slot))
                {{ $slot }}
            @else
                @yield('content')
            @endif
        </main>

        @include('layouts.footer')
    </div>
</body>
</html>
