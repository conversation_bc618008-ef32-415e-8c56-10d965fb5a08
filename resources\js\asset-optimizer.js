/**
 * Asset Optimizer
 * Handles dynamic loading and optimization of assets
 */

class AssetOptimizer {
    constructor() {
        this.loadedAssets = new Set();
        this.preloadQueue = [];
        this.criticalResources = new Set();
        this.isDevelopment = this.detectDevelopmentMode();
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.preloadCriticalAssets();
        this.setupResourceHints();
        this.optimizeImages();
    }

    /**
     * Detect if we're in development mode (Vite dev server)
     */
    detectDevelopmentMode() {
        // Check if Vite dev server hot file exists
        return (
            document.querySelector('script[src*="@vite/client"]') !== null ||
            document.querySelector('script[src*="127.0.0.1:5173"]') !== null ||
            document.querySelector('script[src*="localhost:5173"]') !== null ||
            window.location.hostname === "localhost" ||
            window.location.hostname === "127.0.0.1"
        );
    }

    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver() {
        if ("IntersectionObserver" in window) {
            this.observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            this.loadAsset(entry.target);
                            this.observer.unobserve(entry.target);
                        }
                    });
                },
                {
                    rootMargin: "50px",
                    threshold: 0.1,
                }
            );

            // Observe elements with data-lazy-load attribute
            document.querySelectorAll("[data-lazy-load]").forEach((el) => {
                this.observer.observe(el);
            });
        }
    }

    /**
     * Preload critical assets
     */
    preloadCriticalAssets() {
        // Skip CSS preloading in development mode since Vite handles it
        if (!this.isDevelopment) {
            // Preload critical CSS only in production
            this.preloadResource("/build/assets/app.css", "style");
        } else {
            console.log(
                "Development mode detected - skipping CSS preload (handled by Vite)"
            );
        }

        // Preload critical fonts
        this.preloadResource("/fonts/inter-var.woff2", "font", "font/woff2");

        // Preload hero images
        const heroImages = document.querySelectorAll("[data-hero-image]");
        heroImages.forEach((img) => {
            if (img.dataset.heroImage) {
                this.preloadResource(img.dataset.heroImage, "image");
            }
        });
    }

    /**
     * Setup resource hints
     */
    setupResourceHints() {
        // DNS prefetch for external domains
        this.addResourceHint("//fonts.googleapis.com", "dns-prefetch");
        this.addResourceHint("//fonts.gstatic.com", "dns-prefetch");

        // Preconnect to critical origins
        this.addResourceHint("//fonts.googleapis.com", "preconnect");
        this.addResourceHint("//fonts.gstatic.com", "preconnect");
    }

    /**
     * Add resource hint to document head
     */
    addResourceHint(href, rel, crossorigin = null) {
        const link = document.createElement("link");
        link.rel = rel;
        link.href = href;
        if (crossorigin) {
            link.crossOrigin = crossorigin;
        }
        document.head.appendChild(link);
    }

    /**
     * Preload a resource
     */
    preloadResource(href, as, type = null) {
        if (this.loadedAssets.has(href)) {
            return;
        }

        const link = document.createElement("link");
        link.rel = "preload";
        link.href = href;
        link.as = as;

        if (type) {
            link.type = type;
        }

        if (as === "font") {
            link.crossOrigin = "anonymous";
        }

        link.onload = () => {
            this.loadedAssets.add(href);
        };

        document.head.appendChild(link);
    }

    /**
     * Load an asset dynamically
     */
    async loadAsset(element) {
        const src = element.dataset.lazyLoad;
        const type = element.dataset.assetType || "script";

        if (this.loadedAssets.has(src)) {
            return;
        }

        try {
            if (type === "script") {
                await this.loadScript(src);
            } else if (type === "style") {
                await this.loadStylesheet(src);
            } else if (type === "image") {
                await this.loadImage(element, src);
            }

            this.loadedAssets.add(src);
            element.classList.add("asset-loaded");
        } catch (error) {
            console.error("Failed to load asset:", src, error);
            element.classList.add("asset-error");
        }
    }

    /**
     * Load script dynamically
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement("script");
            script.src = src;
            script.async = true;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Load stylesheet dynamically
     */
    loadStylesheet(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement("link");
            link.rel = "stylesheet";
            link.href = href;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    /**
     * Load image with optimization
     */
    loadImage(element, src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                element.src = src;
                element.classList.add("loaded");
                resolve();
            };
            img.onerror = reject;
            img.src = src;
        });
    }

    /**
     * Optimize images based on device capabilities
     */
    optimizeImages() {
        // Check for WebP support
        this.supportsWebP().then((supported) => {
            if (supported) {
                document.documentElement.classList.add("webp");
                this.convertImagesToWebP();
            }
        });

        // Optimize based on connection speed
        if ("connection" in navigator) {
            const connection = navigator.connection;
            if (
                connection.effectiveType === "slow-2g" ||
                connection.effectiveType === "2g"
            ) {
                this.enableDataSaver();
            }
        }
    }

    /**
     * Check WebP support
     */
    supportsWebP() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src =
                "data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA";
        });
    }

    /**
     * Convert images to WebP where supported
     */
    convertImagesToWebP() {
        const images = document.querySelectorAll("img[data-webp]");
        images.forEach((img) => {
            if (img.dataset.webp) {
                img.src = img.dataset.webp;
            }
        });
    }

    /**
     * Enable data saver mode
     */
    enableDataSaver() {
        document.documentElement.classList.add("data-saver");

        // Reduce image quality
        const images = document.querySelectorAll("img[data-low-quality]");
        images.forEach((img) => {
            if (img.dataset.lowQuality) {
                img.src = img.dataset.lowQuality;
            }
        });

        // Disable non-critical animations
        const style = document.createElement("style");
        style.textContent = `
            .data-saver * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Prefetch next page resources
     */
    prefetchNextPage(url) {
        if (this.loadedAssets.has(url)) {
            return;
        }

        const link = document.createElement("link");
        link.rel = "prefetch";
        link.href = url;
        document.head.appendChild(link);

        this.loadedAssets.add(url);
    }

    /**
     * Critical resource loading
     */
    markAsCritical(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el) => {
            this.criticalResources.add(el);
            el.classList.add("critical-resource");
        });
    }

    /**
     * Cleanup unused resources
     */
    cleanup() {
        // Remove unused preload links
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        preloadLinks.forEach((link) => {
            if (link.sheet || link.complete) {
                // Resource has been used, can remove the preload hint
                setTimeout(() => {
                    if (link.parentNode) {
                        link.parentNode.removeChild(link);
                    }
                }, 1000);
            }
        });
    }
}

// Initialize asset optimizer
document.addEventListener("DOMContentLoaded", () => {
    window.assetOptimizer = new AssetOptimizer();

    // Cleanup on page unload
    window.addEventListener("beforeunload", () => {
        if (window.assetOptimizer) {
            window.assetOptimizer.cleanup();
        }
    });
});

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
    module.exports = AssetOptimizer;
}
