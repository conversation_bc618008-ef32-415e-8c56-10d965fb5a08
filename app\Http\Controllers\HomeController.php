<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Category;
use App\Models\Store;
use App\Models\Coupon;
use App\Models\Banner;
use App\Models\Hero;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class HomeController extends Controller
{
    /**
     * Show the homepage.
     */
    public function index(): View
    {
        $data = Cache::remember('homepage_data', 1800, function () {
            // Active heroes
            $heroes = Hero::active()
                        ->ordered()
                        ->limit(5)
                        ->get();

            // Active banners
            $banners = Banner::active()
                           ->ordered()
                           ->limit(5)
                           ->get();

            // Featured content
            $featuredBlogs = Blog::published()
                               ->featured()
                               ->with(['primaryCategory', 'author'])
                               ->limit(6)
                               ->get();

            // Remove stores and coupons - blog focused

            // Latest content
            $latestBlogs = Blog::published()
                             ->with(['primaryCategory', 'author'])
                             ->recent()
                             ->limit(4)
                             ->get();

            // Popular blogs
            $popularBlogs = Blog::published()
                              ->with(['primaryCategory', 'author'])
                              ->popular()
                              ->limit(4)
                              ->get();

            // Remove coupons - blog focused

            // Categories (blog focused)
            $categories = Category::active()
                                ->whereHas('blogs', function($query) {
                                    $query->published();
                                })
                                ->withCount(['blogs' => function($query) {
                                    $query->published();
                                }])
                                ->ordered()
                                ->limit(6)
                                ->get();

            // Popular tags
            $popularTags = \App\Models\Tag::active()
                                ->whereHas('blogs', function($query) {
                                    $query->published();
                                })
                                ->withCount(['blogs' => function($query) {
                                    $query->published();
                                }])
                                ->orderBy('blogs_count', 'desc')
                                ->limit(12)
                                ->get();

            // Further reading (random published blogs)
            $furtherReading = Blog::published()
                                ->with(['primaryCategory', 'author'])
                                ->inRandomOrder()
                                ->limit(4)
                                ->get();

            // Active partners
            $partners = Partner::active()
                             ->ordered()
                             ->get();

            // Categories for browse section (with images)
            $browseCategories = Category::active()
                                     ->whereHas('blogs', function($query) {
                                         $query->published();
                                     })
                                     ->whereNotNull('image')
                                     ->withCount(['blogs' => function($query) {
                                         $query->published();
                                     }])
                                     ->ordered()
                                     ->limit(8)
                                     ->get();

            return compact(
                'heroes',
                'banners',
                'featuredBlogs',
                'popularBlogs',
                'latestBlogs',
                'categories',
                'popularTags',
                'furtherReading',
                'partners',
                'browseCategories'
            );
        });

        $seoData = [
            'title' => 'Best Deals, Coupons & Lifestyle Content - Your Site Name',
            'description' => 'Discover the best deals, exclusive coupons, and lifestyle content. Save money with verified coupon codes and deals from top brands.',
            'keywords' => ['deals', 'coupons', 'discounts', 'lifestyle', 'shopping', 'savings'],
            'canonical' => url('/'),
            'type' => 'website',
        ];

        return view('home', array_merge($data, compact('seoData')));
    }

    /**
     * Show the about page.
     */
    public function about(): View
    {
        $seoData = [
            'title' => 'About Us - Learn More About Our Mission',
            'description' => 'Learn about our mission to help you save money and discover great lifestyle content.',
            'canonical' => url('/about'),
        ];

        return view('about', compact('seoData'));
    }

    /**
     * Show the contact page.
     */
    public function contact(): View
    {
        $seoData = [
            'title' => 'Contact Us - Get in Touch',
            'description' => 'Have questions or suggestions? Get in touch with our team.',
            'canonical' => url('/contact'),
        ];

        return view('contact', compact('seoData'));
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just return a success message
        
        return back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }

    /**
     * Show the hidden promotions page (accessible only via direct link).
     */
    public function promotions(): View
    {
        $data = Cache::remember('promotions_data', 1800, function () {
            // Featured content
            $featuredCoupons = Coupon::active()
                                   ->featured()
                                   ->with(['store', 'category'])
                                   ->limit(9)
                                   ->get();

            $featuredStores = Store::active()
                                 ->featured()
                                 ->withCount('activeCoupons')
                                 ->limit(8)
                                 ->get();

            // Popular content
            $popularCoupons = Coupon::active()
                                  ->with(['store', 'category'])
                                  ->orderBy('clicks_count', 'desc')
                                  ->limit(9)
                                  ->get();

            $popularStores = Store::active()
                                ->withCount('activeCoupons')
                                ->having('active_coupons_count', '>', 0)
                                ->orderBy('active_coupons_count', 'desc')
                                ->limit(8)
                                ->get();

            // Latest content
            $latestCoupons = Coupon::active()
                                 ->with(['store', 'category'])
                                 ->latest('created_at')
                                 ->limit(9)
                                 ->get();

            $latestStores = Store::active()
                                ->latest('created_at')
                                ->limit(8)
                                ->get();

            return compact(
                'featuredCoupons', 'featuredStores',
                'popularCoupons', 'popularStores',
                'latestCoupons', 'latestStores'
            );
        });

        $seoData = [
            'title' => 'Exclusive Promotions & Special Deals',
            'description' => 'Access our exclusive promotions and special deals available only to our valued visitors.',
            'canonical' => url('/promotions'),
            'robots' => 'noindex,nofollow', // Hidden page
        ];

        return view('promotions', array_merge($data, compact('seoData')));
    }
}
