<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Hero;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;

class HeroController extends Controller
{
    /**
     * Display a listing of heroes.
     */
    public function index(): View
    {
        $heroes = Hero::orderBy('sort_order')->orderBy('created_at', 'desc')->paginate(20);
        return view('admin.heroes.index', compact('heroes'));
    }

    /**
     * Show the form for creating a new hero.
     */
    public function create(): View
    {
        return view('admin.heroes.create');
    }

    /**
     * Store a newly created hero in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'background_color' => 'required|string|max:255',
            'text_color' => 'required|in:white,dark',
            'text_position' => 'required|in:left,center,right',
            'primary_button_text' => 'nullable|string|max:100',
            'primary_button_url' => 'nullable|string|max:500',
            'primary_button_style' => 'required|in:primary,secondary,outline',
            'secondary_button_text' => 'nullable|string|max:100',
            'secondary_button_url' => 'nullable|string|max:500',
            'secondary_button_style' => 'required|in:primary,secondary,outline',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        if ($request->hasFile('background_image')) {
            $validated['background_image'] = $request->file('background_image')->store('heroes', 'public');
        }

        Hero::create($validated);

        return redirect()->route('admin.heroes.index')
                        ->with('success', 'Hero created successfully.');
    }

    /**
     * Display the specified hero.
     */
    public function show(Hero $hero): View
    {
        return view('admin.heroes.show', compact('hero'));
    }

    /**
     * Show the form for editing the specified hero.
     */
    public function edit(Hero $hero): View
    {
        return view('admin.heroes.edit', compact('hero'));
    }

    /**
     * Update the specified hero in storage.
     */
    public function update(Request $request, Hero $hero): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'background_color' => 'required|string|max:255',
            'text_color' => 'required|in:white,dark',
            'text_position' => 'required|in:left,center,right',
            'primary_button_text' => 'nullable|string|max:100',
            'primary_button_url' => 'nullable|string|max:500',
            'primary_button_style' => 'required|in:primary,secondary,outline',
            'secondary_button_text' => 'nullable|string|max:100',
            'secondary_button_url' => 'nullable|string|max:500',
            'secondary_button_style' => 'required|in:primary,secondary,outline',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        if ($request->hasFile('background_image')) {
            // Delete old image
            if ($hero->background_image) {
                Storage::disk('public')->delete($hero->background_image);
            }
            $validated['background_image'] = $request->file('background_image')->store('heroes', 'public');
        }

        $hero->update($validated);

        return redirect()->route('admin.heroes.index')
                        ->with('success', 'Hero updated successfully.');
    }

    /**
     * Remove the specified hero from storage.
     */
    public function destroy(Hero $hero): RedirectResponse
    {
        // Delete associated image
        if ($hero->background_image) {
            Storage::disk('public')->delete($hero->background_image);
        }

        $hero->delete();

        return redirect()->route('admin.heroes.index')
                        ->with('success', 'Hero deleted successfully.');
    }

    /**
     * Toggle the active status of the hero.
     */
    public function toggleActive(Hero $hero): RedirectResponse
    {
        $hero->update(['is_active' => !$hero->is_active]);

        $status = $hero->is_active ? 'activated' : 'deactivated';
        return redirect()->back()->with('success', "Hero {$status} successfully.");
    }
}
