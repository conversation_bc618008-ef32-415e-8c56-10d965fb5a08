@extends('admin.layouts.app')

@section('title', 'Heroes Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Heroes Management</h3>
                    <a href="{{ route('admin.heroes.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Hero
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Position</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($heroes as $hero)
                                    <tr>
                                        <td>{{ $hero->id }}</td>
                                        <td>
                                            <strong>{{ $hero->title }}</strong>
                                            @if($hero->subtitle)
                                                <br><small class="text-muted">{{ Str::limit($hero->subtitle, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($hero->text_position) }}</span>
                                        </td>
                                        <td>
                                            @if($hero->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                            
                                            @if($hero->is_scheduled)
                                                <span class="badge bg-warning">Scheduled</span>
                                            @elseif($hero->is_expired)
                                                <span class="badge bg-danger">Expired</span>
                                            @endif
                                        </td>
                                        <td>{{ $hero->sort_order }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.heroes.show', $hero) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.heroes.edit', $hero) }}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.heroes.toggle-active', $hero) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm {{ $hero->is_active ? 'btn-secondary' : 'btn-success' }}">
                                                        <i class="fas fa-{{ $hero->is_active ? 'pause' : 'play' }}"></i>
                                                    </button>
                                                </form>
                                                <form action="{{ route('admin.heroes.destroy', $hero) }}" method="POST" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this hero?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No heroes found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    {{ $heroes->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
